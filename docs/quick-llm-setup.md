# Quick LLM Setup Guide

## Overview

The Quick LLM Setup feature provides an easy way to configure popular LLM providers with predefined settings. This simplifies the process of adding new LLM configurations, especially for Chinese LLM providers.

## How to Access

1. Open AutoDev Settings (File → Settings → AutoDev)
2. In the Model Management section, click the **"Quick Setup"** button
3. The Quick LLM Setup dialog will open

## Supported Providers

### International Providers
- **OpenAI**: Standard GPT models
- **Azure OpenAI**: Microsoft's OpenAI service
- **Anthropic Claude**: Claude models
- **Google Gemini**: Google's AI models
- **Ollama (Local)**: Local LLM deployment

### Chinese LLM Providers
- **DeepSeek**: High-quality reasoning model
  - URL: `https://api.deepseek.com/v1/chat/completions`
  - Default Model: `deepseek-chat`
  
- **GLM (智谱清言)**: Zhipu AI's conversational model
  - URL: `https://open.bigmodel.cn/api/paas/v4/chat/completions`
  - Default Model: `glm-4`
  
- **<PERSON><PERSON> (通义千问)**: Alibaba's multilingual model
  - URL: `https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation`
  - Default Model: `qwen-turbo`
  
- **Moonshot (月之暗面)**: Long context window support
  - URL: `https://api.moonshot.cn/v1/chat/completions`
  - Default Model: `moonshot-v1-8k`
  
- **Baichuan (百川)**: Specialized for Chinese tasks
  - URL: `https://api.baichuan-ai.com/v1/chat/completions`
  - Default Model: `Baichuan2-Turbo`

## Setup Process

1. **Select Provider**: Choose from the dropdown list
2. **Configure Name**: The configuration name is auto-filled but can be customized
3. **API URL**: Pre-filled with the correct endpoint for the selected provider
4. **API Token**: Enter your API key/token (required for most providers)
5. **Model Name**: Default model is pre-filled but can be changed
6. **Click OK**: The configuration will be saved and added to your available models

## Features

- **Auto-fill URLs**: Correct API endpoints are automatically filled
- **Default Models**: Recommended models are pre-selected
- **Validation**: Required fields are validated before saving
- **Duplicate Check**: Prevents creating configurations with duplicate names
- **Auto-default**: If no default model is set, the new configuration becomes the default

## Benefits

- **Simplified Setup**: No need to manually research API endpoints
- **Chinese LLM Support**: Easy access to popular Chinese AI providers
- **Quick Configuration**: Reduces setup time from minutes to seconds
- **Error Prevention**: Pre-validated URLs and settings reduce configuration errors

## Usage Tips

1. **API Keys**: Make sure to obtain valid API keys from the respective providers
2. **Model Names**: You can change the default model name if you have access to other models
3. **Testing**: After setup, you can test the connection using the existing LLM management features
4. **Multiple Configs**: You can create multiple configurations for the same provider with different models

## Integration

The Quick Setup feature integrates seamlessly with the existing LLM management system:
- New configurations appear in all model dropdown lists
- Can be edited using the standard LLM dialog
- Supports all existing features like streaming, temperature settings, etc.
- Works with category-specific model assignments

## Technical Details

- Configurations are stored in the same format as manually created LLMs
- Uses the standard `LlmConfig` data structure
- Supports all existing authentication and request customization options
- Compatible with the existing model management and testing infrastructure
